# 微信小程序黑名单功能使用说明

## 功能概述

本功能实现了用户登录时的黑名单验证，可以有效阻止恶意用户登录小程序。

## 实现的功能

### 1. 登录时验证
- 用户点击头像登录时，系统会自动检查该用户的 openid 是否在黑名单中
- 如果用户在黑名单中，将阻止其登录并显示封禁信息
- 如果用户不在黑名单中，允许正常登录

### 2. 应用启动时验证
- 应用启动时会检查已登录用户的黑名单状态
- 如果发现已登录用户被加入黑名单，会自动退出登录并跳转到首页

### 3. 过期时间支持
- 支持临时封禁和永久封禁
- 临时封禁到期后会自动解除

## 数据库结构

需要在云数据库中创建 `BanList` 集合，包含以下字段：

```javascript
{
  _id: "记录ID",
  openid: "用户的openid",
  reason: "封禁原因",
  status: "状态：active(生效中)/expired(已过期)/removed(已解除)",
  createTime: "创建时间",
  updateTime: "更新时间",
  expireTime: "过期时间（可选，不设置则为永久封禁）"
}
```

## 文件说明

### 1. 云函数
- `cloudfunctions/checkBanStatus/index.js` - 检查用户黑名单状态的云函数
- `cloudfunctions/checkBanStatus/package.json` - 云函数配置文件

### 2. 工具模块
- `utils/banUtils.js` - 黑名单验证工具模块，包含所有相关方法

### 3. 修改的文件
- `app.js` - 添加了应用启动时的黑名单检查
- `pages/user/user.js` - 添加了登录时的黑名单验证

## 使用方法

### 1. 部署云函数
1. 在微信开发者工具中，右键点击 `cloudfunctions/checkBanStatus` 文件夹
2. 选择"上传并部署：云端安装依赖"

### 2. 创建数据库集合
1. 在云开发控制台中创建 `BanList` 集合
2. 设置合适的权限（建议只允许管理员写入）

### 3. 添加黑名单记录
可以通过以下方式添加黑名单记录：

#### 方式1：直接在数据库中添加
```javascript
{
  openid: "用户的openid",
  reason: "恶意行为",
  status: "active",
  createTime: new Date(),
  updateTime: new Date()
  // expireTime: new Date("2024-12-31") // 可选，设置过期时间
}
```

#### 方式2：使用工具方法（需要在小程序中调用）
```javascript
const banUtils = require('./utils/banUtils.js');

// 永久封禁
banUtils.addToBanList('用户openid', '封禁原因');

// 临时封禁（7天后过期）
const expireDate = new Date();
expireDate.setDate(expireDate.getDate() + 7);
banUtils.addToBanList('用户openid', '封禁原因', expireDate);
```

### 4. 解除封禁
```javascript
const banUtils = require('./utils/banUtils.js');
banUtils.removeFromBanList('用户openid');
```

## 用户体验

### 被封禁用户的体验流程
1. 用户点击头像尝试登录
2. 系统检查黑名单状态
3. 如果用户在黑名单中：
   - 显示封禁提示弹窗
   - 弹窗内容包括封禁原因和解封时间（如果是临时封禁）
   - 用户无法完成登录
4. 如果用户不在黑名单中：
   - 正常完成登录流程

### 已登录用户被封禁的处理
1. 应用启动时自动检查
2. 如果发现用户被封禁：
   - 自动清除登录状态
   - 显示封禁提示
   - 跳转到首页

## 安全特性

1. **容错处理**：网络异常时不会阻止正常用户登录
2. **自动过期**：临时封禁到期后自动解除
3. **状态同步**：应用启动时会检查已登录用户状态
4. **权限控制**：只有管理员可以操作黑名单

## 注意事项

1. 确保云函数已正确部署
2. 确保数据库集合权限设置正确
3. 建议定期清理过期的黑名单记录
4. 封禁操作应谨慎使用，避免误封正常用户

## 测试建议

1. 创建测试用户的黑名单记录
2. 测试登录时的拦截功能
3. 测试应用启动时的检查功能
4. 测试临时封禁的过期机制
