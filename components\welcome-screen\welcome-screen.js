Component({
  /**
   * 组件的属性列表
   */
  properties: {
    visible: {
      type: Boolean,
      value: false
    }
  },

  /**
   * 组件的初始数据
   */
  data: {
    countdown: 50,
    isBanned: false,
    banInfo: null
  },

  /**
   * 组件的生命周期
   */
  lifetimes: {
    attached() {
      // 当组件初始化时，设置倒计时初始值
      this.setData({ countdown: 50 });

      // 检查用户封禁状态
      this.checkBanStatus();

      // 当组件显示时，开始倒计时
      if (this.properties.visible) {
        this.startCountdown();
      }
    },
    detached() {
      // 组件销毁时，清除定时器
      if (this.countdownTimer) {
        clearInterval(this.countdownTimer);
        this.countdownTimer = null;
      }
    }
  },
  
  /**
   * 属性监听器
   */
  observers: {
    'visible': function(visible) {
      if (visible) {
        // 当组件变为可见时，重置倒计时并开始
        this.setData({ countdown: 3 });
        console.log('倒计时开始，初始值：', this.data.countdown);
        this.startCountdown();
      } else {
        // 当组件隐藏时，清除定时器
        if (this.countdownTimer) {
          clearInterval(this.countdownTimer);
          this.countdownTimer = null;
        }
      }
    }
  },

  /**
   * 组件的方法列表
   */
  methods: {
    // 开始倒计时
    startCountdown() {
      // 清除可能存在的旧定时器
      if (this.countdownTimer) {
        clearInterval(this.countdownTimer);
      }
      
      // 设置新的定时器，每秒减少1
      this.countdownTimer = setInterval(() => {
        const newCountdown = this.data.countdown - 1;
        console.log('倒计时更新：', newCountdown);
        
        if (newCountdown <= 0) {
          // 倒计时结束，关闭欢迎页面
          clearInterval(this.countdownTimer);
          this.countdownTimer = null;
          this.triggerEvent('close');
        } else {
          // 更新倒计时
          this.setData({ countdown: newCountdown });
        }
      }, 1000);
    },
    
    // 手动关闭欢迎页面
    onClose() {
      // 检查用户是否被封禁
      if (this.data.isBanned) {
        // 被封禁用户不能进入，显示提示
        wx.showModal({
          title: '账号已被封禁',
          content: `封禁原因：${this.data.banInfo?.reason || '违反平台规定'}\n\n如有疑问，请联系客服。`,
          showCancel: false,
          confirmText: '我知道了',
          confirmColor: '#ff4444'
        });
        return;
      }

      // 添加按钮点击效果
      wx.vibrateShort({
        type: 'light'
      });

      // 清除定时器
      if (this.countdownTimer) {
        clearInterval(this.countdownTimer);
        this.countdownTimer = null;
      }

      // 触发关闭事件
      this.triggerEvent('close');
    },

    // 检查用户封禁状态
    checkBanStatus() {
      const app = getApp();

      // 检查全局数据中的封禁状态
      if (app.globalData.isBanned) {
        this.setData({
          isBanned: true,
          banInfo: app.globalData.banInfo
        });
        return;
      }

      // 检查本地存储中的封禁状态
      const isBanned = wx.getStorageSync('isBanned');
      const banInfo = wx.getStorageSync('banInfo');

      if (isBanned) {
        this.setData({
          isBanned: true,
          banInfo: banInfo
        });

        // 同步到全局数据
        app.globalData.isBanned = true;
        app.globalData.banInfo = banInfo;
      }
    }
  }
}) 