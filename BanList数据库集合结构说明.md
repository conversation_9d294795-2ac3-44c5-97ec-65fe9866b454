# BanList 数据库集合结构说明

## 集合名称
`BanList`

## 字段结构

### 必需字段

| 字段名 | 类型 | 说明 | 示例值 |
|--------|------|------|--------|
| `openid` | String | 被封禁用户的微信openid | "oXXXX-XXXXXXXXXXXXXXXXXXXXXXX" |
| `status` | String | 封禁状态，"active"表示生效，"inactive"表示已解封 | "active" |
| `reason` | String | 封禁原因 | "发布违规内容" |
| `banTime` | Date | 封禁时间 | 2024-01-15T10:30:00.000Z |

### 可选字段

| 字段名 | 类型 | 说明 | 示例值 |
|--------|------|------|--------|
| `adminId` | String | 执行封禁操作的管理员ID | "admin_001" |
| `adminOpenid` | String | 执行封禁操作的管理员openid | "oXXXX-XXXXXXXXXXXXXXXXXXXXXXX" |
| `expireTime` | Date | 封禁到期时间（如果是临时封禁） | 2024-02-15T10:30:00.000Z |
| `unbanTime` | Date | 解封时间（如果已解封） | 2024-01-20T15:45:00.000Z |
| `unbanReason` | String | 解封原因 | "申诉成功" |
| `notes` | String | 备注信息 | "用户多次发布不当内容" |

## 示例数据

### 永久封禁用户
```json
{
  "_id": "ban_001",
  "openid": "oXXXX-XXXXXXXXXXXXXXXXXXXXXXX",
  "status": "active",
  "reason": "恶意刷屏，严重违反平台规定",
  "banTime": "2024-01-15T10:30:00.000Z",
  "adminId": "admin_001",
  "adminOpenid": "oYYYY-YYYYYYYYYYYYYYYYYYYYYYY",
  "notes": "用户在短时间内发布大量垃圾信息，影响平台正常使用"
}
```

### 临时封禁用户
```json
{
  "_id": "ban_002",
  "openid": "oZZZZ-ZZZZZZZZZZZZZZZZZZZZZZZ",
  "status": "active",
  "reason": "发布不当内容",
  "banTime": "2024-01-10T14:20:00.000Z",
  "expireTime": "2024-01-17T14:20:00.000Z",
  "adminId": "admin_002",
  "adminOpenid": "oYYYY-YYYYYYYYYYYYYYYYYYYYYYY",
  "notes": "首次违规，给予7天临时封禁"
}
```

### 已解封用户
```json
{
  "_id": "ban_003",
  "openid": "oAAAA-AAAAAAAAAAAAAAAAAAAAAAAA",
  "status": "inactive",
  "reason": "发布违规内容",
  "banTime": "2024-01-05T09:15:00.000Z",
  "unbanTime": "2024-01-12T16:30:00.000Z",
  "unbanReason": "申诉成功，确认为误判",
  "adminId": "admin_001",
  "adminOpenid": "oYYYY-YYYYYYYYYYYYYYYYYYYYYYY",
  "notes": "用户申诉后核实，确认为系统误判"
}
```

## 使用说明

### 1. 添加封禁用户
在微信小程序云开发控制台的数据库中，向 `BanList` 集合添加记录：

```javascript
// 在云函数中添加封禁记录
const db = cloud.database();
await db.collection('BanList').add({
  data: {
    openid: 'oXXXX-XXXXXXXXXXXXXXXXXXXXXXX',
    status: 'active',
    reason: '发布违规内容',
    banTime: new Date(),
    adminId: 'admin_001',
    notes: '用户发布不当信息'
  }
});
```

### 2. 解封用户
将用户的 `status` 字段更新为 `inactive`：

```javascript
// 在云函数中解封用户
const db = cloud.database();
await db.collection('BanList').where({
  openid: 'oXXXX-XXXXXXXXXXXXXXXXXXXXXXX',
  status: 'active'
}).update({
  data: {
    status: 'inactive',
    unbanTime: new Date(),
    unbanReason: '申诉成功'
  }
});
```

### 3. 查询封禁状态
系统会自动调用 `checkBanStatus` 云函数来检查用户状态，您也可以手动查询：

```javascript
// 查询用户是否被封禁
const db = cloud.database();
const result = await db.collection('BanList').where({
  openid: 'oXXXX-XXXXXXXXXXXXXXXXXXXXXXX',
  status: 'active'
}).get();

const isBanned = result.data.length > 0;
```

## 注意事项

1. **openid 唯一性**：每个用户的 openid 是唯一的，确保封禁记录的准确性
2. **状态管理**：使用 `status` 字段来控制封禁是否生效，便于管理
3. **时间记录**：记录封禁时间和解封时间，便于追踪和审计
4. **原因说明**：详细记录封禁和解封原因，便于后续处理
5. **管理员追踪**：记录执行操作的管理员信息，便于责任追踪

## 权限设置

建议在云开发控制台中设置 `BanList` 集合的权限：
- **读权限**：仅云函数可读
- **写权限**：仅云函数可写
- **删除权限**：仅管理员可删除

这样可以确保封禁数据的安全性和完整性。
