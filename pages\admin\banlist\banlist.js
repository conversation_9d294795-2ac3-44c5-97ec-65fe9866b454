// pages/admin/banlist/banlist.js
// 黑名单管理页面（仅管理员可访问）
const banUtils = require('../../../utils/banUtils.js');

Page({
  data: {
    isAdmin: false,
    banList: [],
    loading: false,
    page: 1,
    pageSize: 20,
    hasMore: true,
    total: 0,
    // 添加封禁模态框
    showAddBanModal: false,
    newBan: {
      openid: '',
      reason: '',
      expireTime: '',
      isPermanent: true
    }
  },

  onLoad: function (options) {
    this.checkAdminPermission();
  },

  onShow: function () {
    if (this.data.isAdmin) {
      this.loadBanList();
    }
  },

  /**
   * 检查管理员权限
   */
  checkAdminPermission: function() {
    const app = getApp();
    const isAdmin = app.globalData.userInfo && app.globalData.userInfo.adm === true;
    
    this.setData({ isAdmin });
    
    if (!isAdmin) {
      wx.showModal({
        title: '权限不足',
        content: '只有管理员才能访问此页面',
        showCancel: false,
        success: () => {
          wx.navigateBack();
        }
      });
      return;
    }
    
    this.loadBanList();
  },

  /**
   * 加载黑名单列表
   */
  loadBanList: function(refresh = false) {
    if (this.data.loading) return;
    
    if (refresh) {
      this.setData({
        page: 1,
        banList: [],
        hasMore: true
      });
    }
    
    this.setData({ loading: true });
    
    const db = wx.cloud.database();
    const { page, pageSize } = this.data;
    
    // 获取总数
    db.collection('BanList').count().then(countRes => {
      const total = countRes.total;
      
      // 获取列表数据
      db.collection('BanList')
        .orderBy('createTime', 'desc')
        .skip((page - 1) * pageSize)
        .limit(pageSize)
        .get()
        .then(res => {
          const newList = refresh ? res.data : [...this.data.banList, ...res.data];
          
          this.setData({
            banList: newList,
            total: total,
            hasMore: newList.length < total,
            loading: false,
            page: refresh ? 2 : page + 1
          });
        })
        .catch(err => {
          console.error('加载黑名单失败:', err);
          this.setData({ loading: false });
          wx.showToast({
            title: '加载失败',
            icon: 'none'
          });
        });
    });
  },

  /**
   * 下拉刷新
   */
  onPullDownRefresh: function() {
    this.loadBanList(true);
    wx.stopPullDownRefresh();
  },

  /**
   * 上拉加载更多
   */
  onReachBottom: function() {
    if (this.data.hasMore && !this.data.loading) {
      this.loadBanList();
    }
  },

  /**
   * 显示添加封禁模态框
   */
  showAddBanModal: function() {
    this.setData({
      showAddBanModal: true,
      newBan: {
        openid: '',
        reason: '',
        expireTime: '',
        isPermanent: true
      }
    });
  },

  /**
   * 隐藏添加封禁模态框
   */
  hideAddBanModal: function() {
    this.setData({ showAddBanModal: false });
  },

  /**
   * 输入框变化处理
   */
  onInputChange: function(e) {
    const { field } = e.currentTarget.dataset;
    const value = e.detail.value;
    
    this.setData({
      [`newBan.${field}`]: value
    });
  },

  /**
   * 切换永久封禁
   */
  togglePermanent: function() {
    this.setData({
      'newBan.isPermanent': !this.data.newBan.isPermanent
    });
  },

  /**
   * 选择过期时间
   */
  onExpireTimeChange: function(e) {
    this.setData({
      'newBan.expireTime': e.detail.value
    });
  },

  /**
   * 添加封禁
   */
  addBan: function() {
    const { openid, reason, expireTime, isPermanent } = this.data.newBan;
    
    if (!openid.trim()) {
      wx.showToast({
        title: '请输入用户OpenID',
        icon: 'none'
      });
      return;
    }
    
    if (!reason.trim()) {
      wx.showToast({
        title: '请输入封禁原因',
        icon: 'none'
      });
      return;
    }
    
    wx.showLoading({ title: '添加中...' });
    
    const expireDate = isPermanent ? null : new Date(expireTime);
    
    banUtils.addToBanList(openid.trim(), reason.trim(), expireDate)
      .then(result => {
        wx.hideLoading();
        wx.showToast({
          title: '封禁成功',
          icon: 'success'
        });
        this.hideAddBanModal();
        this.loadBanList(true);
      })
      .catch(err => {
        wx.hideLoading();
        wx.showToast({
          title: '封禁失败',
          icon: 'none'
        });
        console.error('添加封禁失败:', err);
      });
  },

  /**
   * 解除封禁
   */
  removeBan: function(e) {
    const { openid } = e.currentTarget.dataset;
    
    wx.showModal({
      title: '确认解封',
      content: '确定要解除该用户的封禁吗？',
      success: (res) => {
        if (res.confirm) {
          wx.showLoading({ title: '解封中...' });
          
          banUtils.removeFromBanList(openid)
            .then(result => {
              wx.hideLoading();
              wx.showToast({
                title: '解封成功',
                icon: 'success'
              });
              this.loadBanList(true);
            })
            .catch(err => {
              wx.hideLoading();
              wx.showToast({
                title: '解封失败',
                icon: 'none'
              });
              console.error('解除封禁失败:', err);
            });
        }
      }
    });
  },

  /**
   * 格式化时间
   */
  formatTime: function(time) {
    if (!time) return '永久';
    
    const date = new Date(time);
    return date.toLocaleString('zh-CN', {
      year: 'numeric',
      month: '2-digit',
      day: '2-digit',
      hour: '2-digit',
      minute: '2-digit'
    });
  }
});
