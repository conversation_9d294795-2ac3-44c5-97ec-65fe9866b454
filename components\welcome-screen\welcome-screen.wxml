<!-- components/welcome-screen/welcome-screen.wxml -->
<view class="welcome-container {{visible ? 'visible' : ''}}" wx:if="{{visible}}">
  <view class="welcome-content">
    <!-- 顶部区域 -->
    <view class="welcome-top">
      <!-- 倒计时 - 只在未被封禁时显示 -->
      <view class="countdown-container" wx:if="{{!isBanned}}">
        <view class="countdown">{{countdown}}秒后自动进入</view>
      </view>

      <!-- 封禁提示 - 只在被封禁时显示 -->
      <view class="ban-notice" wx:if="{{isBanned}}">
        <view class="ban-icon">⚠️</view>
        <view class="ban-text">账号已被封禁</view>
      </view>

      <!-- Logo -->
      <view class="logo-container">
        <image class="welcome-logo" src="cloud://miaomuzhongxin-0giu90bpa4cbeaf5.6d69-miaomuzhongxin-0giu90bpa4cbeaf5-1361723888/static/LOG_withoutBackground.png" mode="aspectFit"></image>
      </view>
    </view>
    
    <!-- 中间区域 -->
    <view class="welcome-middle">
      <!-- 欢迎内容 -->
      <view class="welcome-header">
        <view class="welcome-title">欢迎使用</view>
        <view class="welcome-subtitle">成都苗木中心</view>
      </view>
      
      <!-- 欢迎信息 - 根据封禁状态显示不同内容 -->
      <view class="welcome-message" wx:if="{{!isBanned}}">
        <view class="message-text">园林绿化，专业服务商</view>
      </view>

      <!-- 封禁信息 -->
      <view class="ban-message" wx:if="{{isBanned}}">
        <view class="ban-reason">封禁原因：{{banInfo.reason || '违反平台规定'}}</view>
        <view class="ban-contact">如有疑问，请联系客服</view>
      </view>
    </view>
    
    <!-- 底部区域 -->
    <view class="welcome-bottom">
      <!-- 进入按钮 - 根据封禁状态显示不同样式和文本 -->
      <view class="enter-btn {{isBanned ? 'banned' : ''}}" bindtap="onClose">
        <text wx:if="{{!isBanned}}">点击进入</text>
        <text wx:if="{{isBanned}}">账号已封禁</text>
      </view>
    </view>
  </view>
</view> 