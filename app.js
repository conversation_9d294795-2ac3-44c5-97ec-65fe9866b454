// app.js
const mapUtils = require('./utils/mapUtils.js');
const statsUtils = require('./utils/statsUtils.js');

App({
  globalData: {
    userInfo: null,
    isLogined: false,
    userId: null,
    openid: null, // 添加openid字段
    userProvince: null, // 添加用户省份缓存
    cachedAvatarUrl: null,  // 添加全局头像缓存
    shouldShowUserEdit: false,  // 标记是否应该显示编辑个人信息窗口
    connectionState: "DISCONNECTED", // 添加连接状态管理
    socketTask: null, // 保存WebSocket连接实例
    showWelcomeScreen: false, // 控制是否显示欢迎页面
    statusBarHeight: 20, // 状态栏高度，默认20px
    navBarHeight: 44, // 导航栏高度，默认44px
    isBanned: false, // 用户是否被封禁
    banInfo: null // 封禁信息
  },
  
  globalShareInfo: {
    title: '供应~ 求购~ 免费发布，打电话。',
    path: '/pages/home',
    imageUrl: 'https://6d69-miaomuzhongxin-0giu90bpa4cbeaf5-1361723888.tcb.qcloud.la/static/LOG_withoutBackground.png?sign=2203f4de833ea815e4a0da6b50e90ec8&t=1751519553'
  },

 overShare: function () {
    //监听路由切换
    wx.onAppRoute(function (res) {
      let pages = getCurrentPages(),
        view = pages[pages.length - 1]
        if (view) {
          wx.showShareMenu({
            withShareTicket: true,
            menus: ['shareAppMessage', 'shareTimeline']
          })
        }
    })
  },

  onLaunch: function() {

//初始化分享朋友圈
this.overShare();

    // 获取设备信息并设置状态栏高度
   
    this.setSystemInfo();
    
    // 初始化腾讯地图工具
    mapUtils.initQQMapSDK();
    
    // 检查是否是首次启动或版本更新后首次启动
    const launchInfo = wx.getStorageSync('launchInfo') || {};
    const currentVersion = '1.0.0'; // 当前版本号，可以根据实际情况修改
    
    if (!launchInfo.lastLaunchVersion || launchInfo.lastLaunchVersion !== currentVersion) {
      // 首次启动或版本更新后首次启动，显示欢迎页面
      this.globalData.showWelcomeScreen = true;
      
      // 更新启动信息
      wx.setStorageSync('launchInfo', {
        lastLaunchVersion: currentVersion,
        lastLaunchTime: Date.now()
      });
    }
    
    // 确保云环境正确初始化
    try {
      if (!wx.cloud) {
        console.error('请使用 2.2.3 或以上的基础库以使用云能力')
      } else {
        wx.cloud.init({
          // env 参数说明：
        
          traceUser: true,
        });
        
        // 测试云环境连接
        wx.cloud.callFunction({
          name: 'quickstartFunctions',
          data: { type: 'login' },
          success: res => {
            // 连接成功
          },
          fail: err => {
            console.error('云环境连接测试失败:', err);
          }
        });
      }
    } catch (error) {
      console.error('云环境初始化出错:', error);
    }
    
    // 从本地存储中恢复登录状态
    this.restoreLoginState();
  },
  
  // 获取系统信息并设置状态栏高度
  setSystemInfo: function() {
    try {
      const systemInfo = wx.getSystemInfoSync();
      const statusBarHeight = systemInfo.statusBarHeight;
      this.globalData.statusBarHeight = statusBarHeight;
      
      // 获取胶囊按钮位置信息
      const menuButtonInfo = wx.getMenuButtonBoundingClientRect();
      // 计算导航栏高度
      const navBarHeight = (menuButtonInfo.top - statusBarHeight) * 2 + menuButtonInfo.height;
      this.globalData.navBarHeight = navBarHeight;
      
      // 设置全局CSS变量 - 小程序不支持直接设置CSS变量
      // 但我们可以在页面onLoad时通过setData或wx.nextTick动态设置
      // 在各页面使用这些全局数据
      console.log(`状态栏高度: ${statusBarHeight}px, 导航栏高度: ${navBarHeight}px`);
    } catch (e) {
      console.error('获取系统信息失败:', e);
    }
  },
  
  // 恢复登录状态
  restoreLoginState: function() {
    const cachedUserInfo = wx.getStorageSync('userInfo');
    const isLogined = wx.getStorageSync('isLogined');
    const userId = wx.getStorageSync('userId');
    const cachedAvatarUrl = wx.getStorageSync('cachedAvatarUrl');
    const userProvince = wx.getStorageSync('userProvince');
    const openid = wx.getStorageSync('openid'); // 恢复openid

    if (isLogined && cachedUserInfo && userId && openid) {
      // 先恢复基本登录状态
      this.globalData.isLogined = true;
      this.globalData.userInfo = cachedUserInfo;
      this.globalData.userId = userId;
      this.globalData.cachedAvatarUrl = cachedAvatarUrl;
      this.globalData.userProvince = userProvince;
      this.globalData.openid = openid; // 恢复到全局数据

      // 检查用户是否被封禁
      this.checkUserBanStatus(openid);
    }
  },

  // 检查用户封禁状态
  checkUserBanStatus: function(openid) {
    if (!openid) {
      console.log('无openid，跳过封禁状态检查');
      return;
    }

    wx.cloud.callFunction({
      name: 'checkBanStatus',
      data: { openid: openid },
      success: (res) => {
        if (res.result && res.result.success) {
          if (res.result.isBanned) {
            console.log('用户已被封禁:', res.result.banInfo);
            // 处理被封禁用户
            this.handleBannedUser(res.result.banInfo);
          } else {
            console.log('用户状态正常');
          }
        } else {
          console.error('检查封禁状态失败:', res.result);
        }
      },
      fail: (err) => {
        console.error('调用checkBanStatus云函数失败:', err);
      }
    });
  },

  // 处理被封禁用户
  handleBannedUser: function(banInfo) {
    console.log('处理被封禁用户:', banInfo);

    // 清除登录状态
    this.clearLoginState();

    // 设置封禁标记
    this.globalData.isBanned = true;
    this.globalData.banInfo = banInfo;
    wx.setStorageSync('isBanned', true);
    wx.setStorageSync('banInfo', banInfo);

    // 强制显示欢迎页面并禁用进入功能
    this.globalData.showWelcomeScreen = true;

    // 显示封禁提示
    setTimeout(() => {
      wx.showModal({
        title: '账号已被封禁',
        content: `封禁原因：${banInfo.reason || '违反平台规定'}\n\n如有疑问，请联系客服。`,
        showCancel: false,
        confirmText: '我知道了',
        confirmColor: '#ff4444'
      });
    }, 1000);
  },

  // 保存登录状态
  saveLoginState: function(userInfo, userId, openid) {
    // 更新全局数据
    this.globalData.isLogined = true;
    this.globalData.userInfo = userInfo;
    this.globalData.userId = userId;
    
    // 如果提供了openid，保存它
    if (openid) {
      this.globalData.openid = openid;
      wx.setStorageSync('openid', openid);
    }
    
    // 缓存用户省份信息
    if (userInfo && userInfo.province) {
      this.globalData.userProvince = userInfo.province;
      wx.setStorageSync('userProvince', userInfo.province);
      console.log('缓存用户省份信息:', userInfo.province);
    }
    
    // 如果有头像，缓存头像URL
    if (userInfo && userInfo.avatarFileID) {
      this.cacheAvatarUrl(userInfo.avatarFileID);
    }
    
    // 更新登录次数统计
    const db = wx.cloud.database();
    db.collection('appData').doc('61493796683c0eae01a1f4045d9b0e0b').update({
      data: {
        totalLogined: db.command.inc(1)
      }
    }).catch(err => {
      console.error('更新登录统计数据失败:', err);
    });
    
    // 注意：新用户统计已移至generateUniqueNickname函数中直接处理
    
    // 保存到本地存储
    wx.setStorageSync('isLogined', true);
    wx.setStorageSync('userInfo', userInfo);
    wx.setStorageSync('userId', userId);
  },
  
  // 清除登录状态
  clearLoginState: function() {
    // 更新全局数据
    this.globalData.isLogined = false;
    this.globalData.userInfo = null;
    this.globalData.userId = null;
    this.globalData.userProvince = null;
    this.globalData.cachedAvatarUrl = null;
    this.globalData.openid = null; // 清除openid
    this.globalData.isBanned = false; // 清除封禁状态
    this.globalData.banInfo = null; // 清除封禁信息

    // 清除本地存储
    wx.removeStorageSync('isLogined');
    wx.removeStorageSync('userInfo');
    wx.removeStorageSync('userId');
    wx.removeStorageSync('userProvince');
    wx.removeStorageSync('cachedAvatarUrl');
    wx.removeStorageSync('openid'); // 清除openid存储
    wx.removeStorageSync('isBanned'); // 清除封禁状态存储
    wx.removeStorageSync('banInfo'); // 清除封禁信息存储
  },
  
  // 缓存头像URL
  cacheAvatarUrl: function(fileID) {
    if (!fileID) return;
    
    wx.cloud.getTempFileURL({
      fileList: [fileID],
      success: res => {
        if (res.fileList && res.fileList.length > 0) {
          // 获取临时URL并添加时间戳避免缓存问题
          const tempFileURL = res.fileList[0].tempFileURL;
          const cachedUrl = tempFileURL + '?t=' + Date.now();
          
          // 更新全局数据
          this.globalData.cachedAvatarUrl = cachedUrl;
          
          // 更新本地存储
          wx.setStorageSync('cachedAvatarUrl', cachedUrl);
          
          // 如果用户信息存在，同时更新用户信息中的头像
          if (this.globalData.userInfo) {
            this.globalData.userInfo.avatarUrl = cachedUrl;
            wx.setStorageSync('userInfo', this.globalData.userInfo);
          }
        }
      }
    });
  },
  
  // WebSocket连接状态管理
  connectSocket: function(url, options = {}) {
    // 如果已经有连接，先关闭
    this.closeSocket();
    
    // 更新状态为连接中
    if (this.globalData.connectionState !== "CONNECTING") {
      this.globalData.connectionState = "CONNECTING";
    }
    
    try {
      // 创建WebSocket连接
      const socketTask = wx.connectSocket({
        url: url,
        ...options,
        success: () => {
          console.log('WebSocket连接请求已发送');
        },
        fail: (err) => {
          console.error('WebSocket连接请求失败:', err);
          this.globalData.connectionState = "DISCONNECTED";
        }
      });
      
      // 保存socketTask实例
      this.globalData.socketTask = socketTask;
      
      // 监听连接成功
      socketTask.onOpen(() => {
        // 只有在CONNECTING状态才能转为CONNECTED
        if (this.globalData.connectionState === "CONNECTING") {
          this.globalData.connectionState = "CONNECTED";
          console.log('WebSocket连接已建立');
        } else {
          console.warn(`状态错误: 当前状态(${this.globalData.connectionState})不接受"connectionSuccess"`);
          // 强制关闭并重置状态
          this.closeSocket();
        }
      });
      
      // 监听连接关闭
      socketTask.onClose(() => {
        this.globalData.connectionState = "DISCONNECTED";
        this.globalData.socketTask = null;
        console.log('WebSocket连接已关闭');
      });
      
      // 监听连接错误
      socketTask.onError((err) => {
        console.error('WebSocket连接错误:', err);
        this.globalData.connectionState = "DISCONNECTED";
      });
      
      return socketTask;
    } catch (err) {
      console.error('创建WebSocket连接异常:', err);
      this.globalData.connectionState = "DISCONNECTED";
      return null;
    }
  },
  
  // 关闭WebSocket连接
  closeSocket: function() {
    if (this.globalData.socketTask) {
      try {
        this.globalData.socketTask.close({
          success: () => {
            console.log('WebSocket连接关闭请求已发送');
          },
          fail: (err) => {
            console.error('WebSocket连接关闭请求失败:', err);
          },
          complete: () => {
            this.globalData.connectionState = "DISCONNECTED";
            this.globalData.socketTask = null;
          }
        });
      } catch (err) {
        console.error('关闭WebSocket连接异常:', err);
        this.globalData.connectionState = "DISCONNECTED";
        this.globalData.socketTask = null;
      }
    }
  },
  
  /**
   * 此方法已弃用，新用户统计逻辑已移至user.js中的generateUniqueNickname函数
   * @deprecated 已移至user.js中的generateUniqueNickname函数直接处理
   */
  checkAndUpdateNewUser: function(userId) {
    // 此方法已不再使用
    console.log('checkAndUpdateNewUser方法已弃用，新用户统计已移至generateUniqueNickname函数');
    return;
  }
})
