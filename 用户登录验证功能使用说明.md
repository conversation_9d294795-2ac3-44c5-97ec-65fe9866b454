# 微信小程序用户登录验证功能使用说明

## 功能概述

本功能实现了基于黑名单的用户登录验证系统，可以有效阻止恶意用户访问小程序。当用户的 openid 被添加到 BanList 黑名单后，该用户将无法登录小程序，只能停留在欢迎页面。

## 实现的功能

### 1. 用户封禁检查
- 在用户登录时自动检查是否在黑名单中
- 在小程序启动时检查已登录用户的状态
- 实时阻止被封禁用户的登录尝试

### 2. 界面控制
- 被封禁用户只能看到欢迎页面
- 欢迎页面显示封禁状态和原因
- 进入按钮被禁用并显示封禁提示
- 自动隐藏底部导航栏

### 3. 状态管理
- 全局封禁状态管理
- 本地存储封禁信息
- 自动清除被封禁用户的登录状态

## 文件修改说明

### 1. 新增文件

#### `cloudfunctions/checkBanStatus/index.js`
- 云函数，用于检查用户是否在黑名单中
- 根据用户 openid 查询 BanList 集合
- 返回用户封禁状态和相关信息

#### `cloudfunctions/checkBanStatus/package.json`
- 云函数配置文件

### 2. 修改的文件

#### `app.js`
- 添加了全局封禁状态字段 `isBanned` 和 `banInfo`
- 在 `restoreLoginState` 方法中添加封禁状态检查
- 新增 `checkUserBanStatus` 方法用于检查用户封禁状态
- 新增 `handleBannedUser` 方法用于处理被封禁用户
- 修改 `clearLoginState` 方法，同时清除封禁状态

#### `pages/user/user.js`
- 在登录流程中添加封禁状态检查
- 新增 `checkUserBanStatusBeforeLogin` 方法
- 被封禁用户无法完成登录流程

#### `components/welcome-screen/welcome-screen.js`
- 添加封禁状态检查逻辑
- 修改关闭按钮行为，被封禁用户无法关闭欢迎页面
- 显示封禁提示信息

#### `components/welcome-screen/welcome-screen.wxml`
- 根据封禁状态显示不同的界面内容
- 添加封禁提示和原因显示
- 修改按钮文本和状态

#### `components/welcome-screen/welcome-screen.wxss`
- 添加封禁状态相关的样式
- 封禁按钮样式和封禁提示样式

#### `pages/home/<USER>
- 在 `onShow` 方法中检查封禁状态
- 修改 `onWelcomeClose` 方法，阻止被封禁用户关闭欢迎页面

## 使用步骤

### 1. 部署云函数
```bash
# 在微信开发者工具中右键点击 cloudfunctions/checkBanStatus 文件夹
# 选择 "上传并部署：云端安装依赖"
```

### 2. 创建 BanList 数据库集合
在微信小程序云开发控制台中创建名为 `BanList` 的集合，参考 `BanList数据库集合结构说明.md` 文件。

### 3. 添加封禁用户
向 BanList 集合中添加需要封禁的用户记录：

```javascript
// 示例：添加封禁用户
{
  "openid": "oXXXX-XXXXXXXXXXXXXXXXXXXXXXX",
  "status": "active",
  "reason": "发布违规内容",
  "banTime": "2024-01-15T10:30:00.000Z",
  "adminId": "admin_001"
}
```

### 4. 测试功能
1. 添加一个测试用户到黑名单
2. 使用该用户的微信账号打开小程序
3. 验证用户无法登录，只能停留在欢迎页面
4. 确认欢迎页面显示封禁提示

## 工作流程

### 正常用户登录流程
1. 用户点击头像登录
2. 获取用户 openid
3. 调用 `checkBanStatus` 云函数检查封禁状态
4. 如果未被封禁，继续正常登录流程
5. 完成登录，可以正常使用小程序

### 被封禁用户处理流程
1. 用户尝试登录或小程序启动时检查封禁状态
2. 发现用户在黑名单中
3. 清除用户的登录状态
4. 设置全局封禁标记
5. 强制显示欢迎页面
6. 隐藏底部导航栏
7. 显示封禁提示信息
8. 禁用进入按钮

## 管理操作

### 封禁用户
```javascript
// 在云函数中添加封禁记录
const db = cloud.database();
await db.collection('BanList').add({
  data: {
    openid: '用户的openid',
    status: 'active',
    reason: '封禁原因',
    banTime: new Date(),
    adminId: '管理员ID'
  }
});
```

### 解封用户
```javascript
// 在云函数中更新封禁状态
const db = cloud.database();
await db.collection('BanList').where({
  openid: '用户的openid',
  status: 'active'
}).update({
  data: {
    status: 'inactive',
    unbanTime: new Date(),
    unbanReason: '解封原因'
  }
});
```

## 注意事项

1. **云函数权限**：确保 `checkBanStatus` 云函数有读取 BanList 集合的权限
2. **数据库权限**：建议设置 BanList 集合仅云函数可读写
3. **错误处理**：即使检查封禁状态失败，也不会影响正常用户的登录
4. **性能考虑**：封禁检查会在每次登录时执行，但不会显著影响性能
5. **用户体验**：被封禁用户会看到明确的封禁提示，避免困惑

## 扩展功能建议

1. **临时封禁**：支持设置封禁到期时间
2. **封禁等级**：支持不同程度的限制（如只读模式）
3. **申诉功能**：允许用户提交申诉请求
4. **批量管理**：支持批量封禁和解封操作
5. **日志记录**：记录所有封禁相关的操作日志

## 故障排除

### 常见问题
1. **云函数调用失败**：检查云函数是否正确部署
2. **数据库权限错误**：确认 BanList 集合权限设置
3. **封禁状态不生效**：检查 openid 是否正确，status 是否为 'active'
4. **界面显示异常**：检查组件样式文件是否正确加载

### 调试方法
1. 查看云函数日志
2. 检查控制台输出
3. 验证数据库记录
4. 测试不同用户状态
