// pages/home/<USER>
const mapUtils = require('../../utils/mapUtils.js');

Page({

  /**
   * 页面的初始数据
   */
  data: {
    scrollTop: 0,
    backTopTheme: 'round',
    backTopText: '顶部',
    // 用户登录状态 
    isLogined: false,
    userInfo: null,
    userId: null,
    // 树叶动画参数
    leaves: [],
    // 控制叶子动画的可见性
    leavesVisible: true,
    // 树叶动画的z-index值，可以动态调整
    leavesZIndex: 8,
    // 是否正在重启动画
    isRestartingAnimation: false,
    // 当前叶子唯一ID计数器
    leafIdCounter: 0,
    // 气泡动画参数
    bubbles: [],
    // 控制气泡动画的可见性
    bubblesVisible: true,
    // 当前气泡唯一ID计数器
    bubbleIdCounter: 0,
    // 通知列表数据 - 修改为空数组，将从数据库加载
    noticeList: [],
    // 当前激活的通知ID（用于点击效果）
    activeNoticeId: null,
    // 滚动定时器
    scrollTimer: null,
    // 恢复滚动定时器
    resumeScrollTimer: null,
    // 是否用户正在滚动
    userScrolling: false,
    // 用户停止滚动的位置
    userScrollPosition: 0,
    // 记录内容高度和容器高度
    contentHeight: 0,
    containerHeight: 0,
    // 滚动位置
    noticeScrollTop: 0,
    // 页面是否可见
    isPageVisible: false,
    // 统计数据
    statsData: {
      postsCount: '0',
      loginsCount: '0',
      usersCount: '0',
      activePostsCount: '0'
    },
    // 欢迎页面显示控制
    showWelcomeScreen: false
  },
  



  /**
   * 生命周期函数--监听页面加载
   */
  onLoad() {
    // 检查是否需要显示欢迎页面
    const app = getApp();

    // 强制显示欢迎页面用于测试
    this.setData({
      showWelcomeScreen: true
    });

    // 如果显示欢迎页面，则隐藏tabBar
    if (this.data.showWelcomeScreen) {
      if (typeof this.getTabBar === 'function') {
        this.getTabBar().setData({
          hidden: true
        });
      }
    }

    // 生成树叶动画数据
    this.generateLeaves();

    // 生成气泡动画数据
    this.generateBubbles();

    // 加载通知数据
    this.fetchLatestNotices();

    // 检查用户登录状态并更新位置信息，即使在首次加载和欢迎页面显示时也执行
    const isLoggedIn = this.checkLoginStatus();
    if (isLoggedIn && this.data.userId) {
      console.log('页面加载时更新用户位置信息');
      this.updateUserLocationAndProvince(this.data.userId);
    }
  },

  /**
   * 欢迎页面关闭事件处理
   */
  onWelcomeClose() {
    console.log('欢迎页面关闭');

    // 检查用户是否被封禁
    const app = getApp();
    if (app.globalData.isBanned) {
      console.log('用户被封禁，不允许关闭欢迎页面');
      return;
    }

    this.setData({
      showWelcomeScreen: false
    });

    // 更新全局状态
    app.globalData.showWelcomeScreen = false;

    // 显示tabBar
    if (typeof this.getTabBar === 'function') {
      this.getTabBar().setData({
        active: 0,
        hidden: false
      });
    }
    
    // 再次检查用户登录状态和更新位置信息，确保在欢迎页面关闭后更新
    const isLoggedIn = this.checkLoginStatus();
    if (isLoggedIn && this.data.userId) {
      console.log('欢迎页面关闭后更新用户位置信息');
      this.updateUserLocationAndProvince(this.data.userId);
    }
  },

  /**
   * 生命周期函数--监听页面初次渲染完成
   */
  onReady() {
    // 初始化公告滚动
    this.initNoticeScroll();
  },

  /**
   * 生命周期函数--监听页面显示
   */
  onShow() {
    // 设置页面为可见状态
    this.setData({
      isPageVisible: true
    });

    // 检查用户封禁状态
    const app = getApp();
    if (app.globalData.isBanned) {
      // 如果用户被封禁，强制显示欢迎页面并隐藏tabBar
      this.setData({
        showWelcomeScreen: true
      });

      if (typeof this.getTabBar === 'function') {
        this.getTabBar().setData({
          hidden: true
        });
      }
      return;
    }

    // 设置底部tabBar选中状态
    if (typeof this.getTabBar === 'function') {
      this.getTabBar().setData({
        active: 0  // 首页现在是第一个标签，索引为0
      });
    }

    // 检查用户登录状态 - 无论欢迎页面是否显示都检查
    const isLoggedIn = this.checkLoginStatus();

    // 如果用户已登录，检查并更新位置和省份信息
    if (isLoggedIn && this.data.userId) {
      this.updateUserLocationAndProvince(this.data.userId);
    }
    
    // 延迟显示树叶动画，避免一次性创建过多动画元素
    setTimeout(() => {
      if (this.data.isPageVisible) {
        // 当页面显示时，显示树叶动画和气泡动画
        this.setData({
          leavesVisible: true,
          bubblesVisible: true,
        });

        // 如果叶子数量为0，重新生成
        if (this.data.leaves.length === 0) {
          this.generateLeaves();
        }

        // 如果气泡数量为0，重新生成
        if (this.data.bubbles.length === 0) {
          this.generateBubbles();
        }
      }
    }, 300);
    
    // 每次页面显示时重新加载通知数据
    this.fetchLatestNotices();
    
    // 获取平台统计数据
    this.fetchPlatformStats();
    
    // 延迟初始化公告滚动，避免DOM还未完全准备好
    setTimeout(() => {
      if (this.data.isPageVisible) {
        // 初始化公告滚动
        this.initNoticeScroll();
      }
    }, 500);
  },
  
  /**
   * 检查并更新用户的位置和省份信息
   */
  updateUserLocationAndProvince(userId) {
    if (!userId) return;
    
    const db = wx.cloud.database();
    db.collection('users').doc(userId).get().then(res => {
      const userData = res.data;
      const currentTime = new Date();
      const lastUpdateTime = userData.updateTime ? new Date(userData.updateTime) : null;
      
      // 如果上次更新时间距今超过4小时或未设置过更新时间，则更新位置信息
      const shouldUpdate = !lastUpdateTime || ((currentTime - lastUpdateTime) / (1000 * 60 * 60) > 4);
      
      if (shouldUpdate) {
        console.log('用户位置信息需要更新...');
        // 获取位置和省份信息并更新
        mapUtils.getCurrentLocationAndReverse({
          success: (result) => {
            if (result.status === 0) {
              const addressComponent = result.result.address_component;
              const province = addressComponent.province;
              const latitude = result.result.location.lat;
              const longitude = result.result.location.lng;
              
              // 更新用户的省份和位置信息
              db.collection('users').doc(userId).update({
                data: {
                  province: province,
                  nowPos: db.Geo.Point(longitude, latitude),
                  updateTime: db.serverDate()
                }
              }).then(() => {
              
                
                // 更新全局数据
                const app = getApp();
                if (app.globalData.userInfo) {
                  app.globalData.userInfo.province = province;
                  app.globalData.userProvince = province;
                  wx.setStorageSync('userProvince', province);
                }
              }).catch(err => {
             
              });
            }
          },
          fail: (err) => {
          
          }
        });
      } else {
      
      }
    }).catch(err => {
      console.error('获取用户数据失败:', err);
    });
  },

  /**
   * 检查用户登录状态
   * @returns {boolean} 是否已登录
   */
  checkLoginStatus() {
    // 获取App实例
    const app = getApp();
    
    // 从全局状态中获取登录状态
    if (app.globalData.isLogined && app.globalData.userInfo && app.globalData.userId) {
      this.setData({
        isLogined: true,
        userInfo: app.globalData.userInfo,
        userId: app.globalData.userId
      });
      return true;
    } else {
      this.setData({
        isLogined: false,
        userInfo: null,
        userId: null
      });
      
      return false;
    }
  },

  /**
   * 生命周期函数--监听页面隐藏
   */
  onHide() {
    // 设置页面为不可见状态
    this.setData({
      isPageVisible: false,
      // 隐藏树叶动画和气泡动画
      leavesVisible: false,
      bubblesVisible: false
    });
    
    // 清除滚动定时器
    if (this.data.scrollTimer) {
      clearInterval(this.data.scrollTimer);
      this.data.scrollTimer = null;
    }
    
    // 清除恢复滚动的定时器
    if (this.data.resumeScrollTimer) {
      clearTimeout(this.data.resumeScrollTimer);
      this.data.resumeScrollTimer = null;
    }
    
    // 使用nextTick确保数据更新后再执行一次清理
    wx.nextTick(() => {
      // 再次检查并清除定时器，确保清理彻底
      if (this.data && this.data.scrollTimer) {
        clearInterval(this.data.scrollTimer);
        this.data.scrollTimer = null;
      }
      
      if (this.data && this.data.resumeScrollTimer) {
        clearTimeout(this.data.resumeScrollTimer);
        this.data.resumeScrollTimer = null;
      }
    });
  },

  /**
   * 生命周期函数--监听页面卸载
   */
  onUnload() {
    // 设置页面为不可见状态
    this.setData({
      isPageVisible: false,
      // 隐藏树叶动画和气泡动画
      leavesVisible: false,
      bubblesVisible: false,
      // 清空叶子数组和气泡数组，释放内存
      leaves: [],
      bubbles: []
    });
    
    // 清除滚动定时器
    if (this.data.scrollTimer) {
      clearInterval(this.data.scrollTimer);
      this.data.scrollTimer = null;
    }
    
    // 清除恢复滚动的定时器
    if (this.data.resumeScrollTimer) {
      clearTimeout(this.data.resumeScrollTimer);
      this.data.resumeScrollTimer = null;
    }
    
    // 使用nextTick确保数据更新后再执行一次清理
    wx.nextTick(() => {
      // 再次检查并清除定时器，确保清理彻底
      if (this.data && this.data.scrollTimer) {
        clearInterval(this.data.scrollTimer);
        this.data.scrollTimer = null;
      }
      
      if (this.data && this.data.resumeScrollTimer) {
        clearTimeout(this.data.resumeScrollTimer);
        this.data.resumeScrollTimer = null;
      }
    });
  },

  /**
   * 监听页面滚动事件
   */
  onPageScroll(e) {
    // 更新滚动位置
    this.setData({
      scrollTop: e.scrollTop
    });
  },

  /**
   * 用户点击右上角分享
   */
  onShareAppMessage() {
    return {
      title: '工程直采-农户直卖',
      path: '/pages/home/<USER>'
    };
  },

  // 回到顶部
  onToTop() {
    // 检查页面是否可见
    if (!this.data || !this.data.isPageVisible) {
      return;
    }
    
    try {
      const query = wx.createSelectorQuery().in(this);
      query.select('.home-content').node().exec((res) => {
        if (!res || !res[0] || !res[0].node) {
          return;
        }
        const scrollView = res[0].node;
        scrollView.scrollTop = 0;
      });
    } catch (err) {
      console.error('回到顶部操作失败:', err);
      // 尝试使用替代方案滚动到顶部
      wx.pageScrollTo({
        scrollTop: 0,
        duration: 300
      });
    }
  },

  // 处理树叶动画结束
  onLeafAnimationEnd(e) {
    // 获取结束动画的叶子ID
    const leafId = e.currentTarget.dataset.leafId;
    
    // 从数组中移除该叶子
    const updatedLeaves = this.data.leaves.filter(leaf => leaf.id !== leafId);
    
    this.setData({
      leaves: updatedLeaves
    });
    
    // 如果叶子数量太少且页面可见，生成新的叶子
    // 限制叶子总数，避免过多动画元素
    if (updatedLeaves.length < 3 && this.data.isPageVisible && updatedLeaves.length < 6) {
      this.generateNewLeaf();
    }
  },

  // 生成一片新叶子
  generateNewLeaf() {
    // 如果页面不可见，不生成新叶子
    if (!this.data.isPageVisible) {
      return;
    }
    
    // 限制最大叶子数量为6个
    if (this.data.leaves.length >= 6) {
      return;
    }
    
    const newLeafId = this.data.leafIdCounter + 1;
    
    // 创建一个新叶 子对象
    const newLeaf = {
      id: newLeafId,
      type: Math.floor(Math.random() * 2) + 1, // 1或2，对应不同类型叶子
      size: Math.floor(Math.random() * 65) + 60, // 45-110rpx的尺寸
      startPos: Math.random() * 100, // 随机水平位置
      delay: Math.random() * 2, // 0-2秒的随机延迟
      duration: Math.floor(Math.random() * 14) + 17 // 17-31秒的下落时间
    };
    
    // 添加到现有叶子数组
    const updatedLeaves = [...this.data.leaves, newLeaf];
    
    // 更新状态
    this.setData({   
      leaves: updatedLeaves,
      leafIdCounter: newLeafId
    });
  },

  // 生成树叶动画相关数据
  generateLeaves() {
    // 如果页面不可见，不生成叶子
    if (!this.data.isPageVisible) {
      return;
    }
    
    // 减少初始叶子数量
    const leavesCount = Math.floor(Math.random() * 3) + 2; // 2-4片叶子
    const leaves = [];
    let leafIdCounter = 0;
    
    // 确保叶子分布在整个屏幕宽度
    const screenSegments = 4;
    const segmentWidth = 100 / screenSegments;
    
    for (let i = 0; i < leavesCount; i++) {
      // 计算该叶子应该在哪个屏幕区域
      const segment = i % screenSegments;
      // 在该区域内随机位置
      const basePos = segment * segmentWidth;
      const randomOffset = Math.random() * segmentWidth;
      const startPos = basePos + randomOffset;
      
      // 使用自增ID确保唯一性
      leafIdCounter++;
      
      leaves.push({
        id: leafIdCounter,
        type: Math.floor(Math.random() * 2) + 1, // 1或2，对应不同类型叶子
        size: Math.floor(Math.random() * 65) + 50, // 45-110rpx的尺寸
        startPos: startPos, // 分布在整个屏幕宽度的随机水平位置
        delay: Math.random() * 8, // 0-8秒的随机延迟
        duration: Math.floor(Math.random() * 14) + 17 // 17-31秒的下落时间
      });
    }
    
    this.setData({
      leaves,
      leavesVisible: true,
      leavesZIndex: 8,
      leafIdCounter
    });
  },

  // 处理气泡动画结束
  onBubbleAnimationEnd(e) {
    // 获取结束动画的气泡ID
    const bubbleId = e.currentTarget.dataset.bubbleId;

    // 从数组中移除该气泡
    const updatedBubbles = this.data.bubbles.filter(bubble => bubble.id !== bubbleId);

    this.setData({
      bubbles: updatedBubbles
    });

    // 如果气泡数量太少且页面可见，生成新的气泡
    // 限制气泡总数，避免过多动画元素
    if (updatedBubbles.length < 4 && this.data.isPageVisible && updatedBubbles.length < 8) {
      this.generateNewBubble();
    }
  },

  // 生成一个新气泡
  generateNewBubble() {
    // 如果页面不可见，不生成新气泡
    if (!this.data.isPageVisible) {
      return;
    }

    // 限制最大气泡数量为8个
    if (this.data.bubbles.length >= 8) {
      return;
    }

    const newBubbleId = this.data.bubbleIdCounter + 1;

    // 创建一个新气泡对象，参考app-stats-footer的模糊气泡样式
    const newBubble = {
      id: newBubbleId,
      size: Math.floor(Math.random() * 50) + 40, // 40-90rpx的尺寸，模仿app-stats-footer的气泡大小
      startPos: Math.random() * 50 + 40, // 40%-90%的随机水平位置，偏向右侧避开左边文字
      delay: Math.random() * 2, // 0-2秒的随机延迟，减少等待时间
      duration: Math.floor(Math.random() * 10) + 15, // 10-25秒的动画时间，包含等待时间
      opacity: Math.random() * 0.3 + 0.15 // 0.15-0.45的透明度，更接近模糊气泡效果
    };

    // 添加到现有气泡数组
    const updatedBubbles = [...this.data.bubbles, newBubble];

    // 更新状态
    this.setData({
      bubbles: updatedBubbles,
      bubbleIdCounter: newBubbleId
    });
  },

  // 生成气泡动画相关数据
  generateBubbles() {
    // 如果页面不可见，不生成气泡
    if (!this.data.isPageVisible) {
      return;
    }

    // 初始气泡数量
    const bubblesCount = Math.floor(Math.random() * 3) + 3; // 3-5个气泡
    const bubbles = [];
    let bubbleIdCounter = 0;

    // 确保气泡分布在卡片右侧区域，避开左边文字
    const screenSegments = 4;
    const segmentWidth = 50 / screenSegments; // 右侧50%宽度分成4段

    for (let i = 0; i < bubblesCount; i++) {
      // 计算该气泡应该在哪个区域
      const segment = i % screenSegments;
      // 在该区域内随机位置，从40%开始（右侧区域）
      const basePos = segment * segmentWidth + 40;
      const randomOffset = Math.random() * segmentWidth;
      const startPos = basePos + randomOffset;

      // 使用自增ID确保唯一性
      bubbleIdCounter++;

      bubbles.push({
        id: bubbleIdCounter,
        size: Math.floor(Math.random() * 55) + 35, // 35-90rpx的尺寸，模仿app-stats-footer的气泡
        startPos: startPos, // 分布在卡片宽度的随机水平位置
        delay: i < 2 ? i * 0.5 : Math.random() * 3, // 前两个气泡立即显示，其他0-3秒延迟
        duration: Math.floor(Math.random() * 5) + 5, // 14-26秒的动画时间，包含等待时间
        opacity: Math.random() * 0.3 + 0.15 // 0.15-0.45的透明度，模糊气泡效果
      });
    }

    this.setData({
      bubbles,
      bubblesVisible: true,
      bubbleIdCounter
    });
  },

  /**
   * 清理页面资源，在跳转前调用
   * @private
   */
  _cleanupBeforeNavigation() {
    // 设置页面为不可见状态，停止所有查询和定时器
    this.setData({
      isPageVisible: false
    });
    
    // 清除滚动定时器
    if (this.data.scrollTimer) {
      clearInterval(this.data.scrollTimer);
      this.data.scrollTimer = null;
    }
    
    // 清除恢复滚动的定时器
    if (this.data.resumeScrollTimer) {
      clearTimeout(this.data.resumeScrollTimer);
      this.data.resumeScrollTimer = null;
    }
  },

  // 求购按钮点击事件
  onDemandTap() {
    this._cleanupBeforeNavigation();
    wx.switchTab({
      url: '/pages/demand/demand'
    });
  },

  // 供应按钮点击事件
  onSupplyTap() {
    this._cleanupBeforeNavigation();
    wx.switchTab({
      url: '/pages/supply/supply'
    });
  },

  /**
   * 跳转到求购发布页面
   */
  onCreateDemandTap() {
    // 如果欢迎页面正在显示，先关闭欢迎页面
    if (this.data.showWelcomeScreen) {
      this.onWelcomeClose();
      return;
    }
    
    this._cleanupBeforeNavigation();
    wx.navigateTo({
      url: '/pages/demand/publish/publish'
    });
  },
  
  /**
   * 跳转到供应发布页面
   */
  onCreateSupplyTap() {
    // 如果欢迎页面正在显示，先关闭欢迎页面
    if (this.data.showWelcomeScreen) {
      this.onWelcomeClose();
      return;
    }
    
    this._cleanupBeforeNavigation();
    wx.navigateTo({
      url: '/pages/supply/publish/publish'
    });
  },
  
  /**
   * 点击搜索栏跳转到供应页面
   */
  navigateToSupply() {
    this._cleanupBeforeNavigation();
    wx.switchTab({
      url: '/pages/supply/supply'
    });
  },
  
  // 处理通知项点击事件
  onNoticeItemTap(e) {
    // 如果欢迎页面正在显示，先关闭欢迎页面
    if (this.data.showWelcomeScreen) {
      this.onWelcomeClose();
      return;
    }
    
    const noticeId = e.currentTarget.dataset.id;
    
    // 设置激活的通知ID以显示点击效果
    this.setData({
      activeNoticeId: noticeId
    });
    
    // 查找点击的通知内容
    const noticeItem = this.data.noticeList.find(item => item.id === noticeId);
    
    if (noticeItem) {
      // 100毫秒后清除点击效果
      setTimeout(() => {
        this.setData({
          activeNoticeId: null
        });
      }, 100);
      
      // 根据通知类型导航到相应页面
      if (noticeItem.postId) {
        if (noticeItem.postType === 'supply') {
          // 跳转到供应详情页
          wx.navigateTo({
            url: `/pages/supply/detail/detail?id=${noticeItem.postId}`,
            fail: (err) => {
              console.error('跳转供应详情页失败:', err);
              wx.showToast({
                title: '该供应信息可能已删除',
                icon: 'none'
              });
            }
          });
        } else if (noticeItem.postType === 'demand') {
          // 跳转到需求详情页
          wx.navigateTo({
            url: `/pages/demand/detail/detail?id=${noticeItem.postId}`,
            fail: (err) => {
             
              wx.showToast({
                title: '该需求信息可能已删除',
                icon: 'none'
              });
            }
          });
        }
      } else {
        // 没有关联的帖子ID
        wx.showToast({
          title: '找不到相关内容',
          icon: 'none'
        });
      }
    }
  },
  
  // 初始化公告滚动
  initNoticeScroll() {
    // 检查页面是否可见
    if (!this.data || !this.data.isPageVisible) {
      return;
    }
    
    // 先清除可能存在的定时器
    if (this.data.scrollTimer) {
      clearInterval(this.data.scrollTimer);
      this.setData({
        scrollTimer: null
      });
    }
    
    // 清除可能存在的恢复滚动定时器
    if (this.data.resumeScrollTimer) {
      clearTimeout(this.data.resumeScrollTimer);
      this.setData({
        resumeScrollTimer: null
      });
    }
    
    // 获取通知容器和内容的高度
    try {
      // 确保在当前页面的上下文中创建查询
      const query = wx.createSelectorQuery().in(this);
      query.select('.notice-marquee-wrapper-full').boundingClientRect(); // 修正选择器名称
      query.select('.notice-marquee').boundingClientRect();
      query.exec((res) => {
        // 再次检查页面状态
        if (!res || !res[0] || !res[1] || !this.data || !this.data.isPageVisible) {
          return;
        }
        
        const containerHeight = res[0].height;
        const contentHeight = res[1].height;
      
        
        // 保存高度信息
        this.setData({
          containerHeight: containerHeight,
          contentHeight: contentHeight
        });
        
        // 只有当内容高度大于容器高度时才需要滚动
        if (contentHeight > containerHeight) {
          // 重置滚动位置到顶部
          this.setData({
            noticeScrollTop: 0,
            userScrollPosition: 0
          });
          
          // 延迟启动自动滚动
          setTimeout(() => {
            if (this.data && this.data.isPageVisible) {
              this.startAutoScroll();
            }
          }, 1000); // 减少延迟时间
        }
      });
    } catch (err) {
    
    }
  },
  
  // 开始自动滚动
  startAutoScroll() {
    // 检查页面是否可见
    if (!this.data || !this.data.isPageVisible) {
      return;
    }
    
    // 先清除可能存在的定时器
    if (this.data.scrollTimer) {
      clearInterval(this.data.scrollTimer);
      this.setData({
        scrollTimer: null
      });
    }
    
    // 创建新的定时器
    const timer = setInterval(() => {
      // 检查页面状态
      if (!this || !this.data || !this.data.isPageVisible) {
        clearInterval(timer);
        return;
      }

      // 如果用户正在滚动，暂停自动滚动
      if (this.data.userScrolling) {
        return;
      }
      
      // 获取当前滚动位置
      const query = wx.createSelectorQuery().in(this);
      query.select('.notice-marquee-wrapper-full').scrollOffset(); // 修正选择器名称
      query.select('.notice-marquee').boundingClientRect();
      query.exec((res) => {
        if (!res || !res[0] || !res[1] || !this.data || !this.data.isPageVisible) {
          return;
        }
        
        const currentScrollTop = res[0].scrollTop;
        const contentHeight = res[1].height;
        const containerHeight = this.data.containerHeight;
        
        // 计算新的滚动位置
        let newScrollTop = currentScrollTop + 40; // 每次滚动一条消息的高度
        
        // 如果滚动到底部，重置到顶部
        if (newScrollTop >= contentHeight - containerHeight) {
          newScrollTop = 0;
        }
        
        // 使用动画平滑滚动
        this.setData({
          noticeScrollTop: newScrollTop
        });
      });
    }, 2000); // 每2秒滚动一次
    
    // 保存定时器ID
    this.setData({
      scrollTimer: timer
    });
  },
  
  // 处理用户触摸开始事件
  onNoticeTouchStart() {
    // 检查页面是否可见
    if (!this.data || !this.data.isPageVisible) {
      return;
    }
    
    // 标记用户正在滚动
    this.setData({ userScrolling: true });
    
    // 清除自动滚动定时器
    if (this.data.scrollTimer) {
      clearInterval(this.data.scrollTimer);
      this.data.scrollTimer = null;
    }
    
    // 清除恢复滚动的定时器
    if (this.data.resumeScrollTimer) {
      clearTimeout(this.data.resumeScrollTimer);
      this.data.resumeScrollTimer = null;
    }
  },
  
  // 处理用户触摸结束事件 - 优化版
  onNoticeTouchEnd() {
    // 如果页面已隐藏或卸载，不执行后续操作
    if (!this.data || !this.data.isPageVisible) {
      return;
    }
    
    // 查询当前的滚动位置
    try {
      // 确保在当前页面的上下文中创建查询
      const query = wx.createSelectorQuery().in(this);
      query.select('.notice-marquee-wrapper-full').scrollOffset(); // 修正选择器名称
      query.exec((res) => {
        if (!res || !res[0] || !this.data || !this.data.isPageVisible) {
          return;
        }
        
        const currentPosition = res[0].scrollTop;
        
        // 保存用户停止滚动的位置
        this.setData({
          userScrollPosition: currentPosition,
          userScrolling: false,
          noticeScrollTop: currentPosition
        });
        
        // 清除之前的恢复滚动定时器
        if (this.data.resumeScrollTimer) {
          clearTimeout(this.data.resumeScrollTimer);
        }
        
        // 延迟5秒后从用户停留的位置继续自动滚动
        // 给用户足够的时间阅读当前位置的消息
        const timerID = setTimeout(() => {
          // 检查页面是否可见
          if (!this.data || !this.data.isPageVisible) {
            return;
          }
          
          // 重新启动自动滚动
          this.startAutoScroll();
        }, 2500); // 延迟5秒再恢复自动滚动
        
        // 保存定时器ID
        this.setData({
          resumeScrollTimer: timerID
        });
      });
    } catch (err) {
     
      
      // 发生错误时，重置用户滚动状态
      this.setData({
        userScrolling: false
      });
      
      // 尝试重新启动自动滚动
      this.startAutoScroll();
    }
  },
  
  // 处理通知滚动事件 - 优化版
  onNoticeScroll(e) {
    // 检查页面是否可见
    if (!this.data || !this.data.isPageVisible) {
      return;
    }
    
    // 保存当前滚动位置
    const scrollTop = e.detail.scrollTop;
    
    // 减少不必要的setData调用，只有在位置变化大于2像素时才更新
    if (Math.abs(scrollTop - this.data.userScrollPosition) > 2) {
      this.setData({
        userScrollPosition: scrollTop
      });
    }
    
    // 如果是自动滚动且不是用户手动操作，不做特殊处理
    if (!this.data.userScrolling) {
      return;
    }

    // 用户手动滚动时，暂时停止自动滚动定时器
    if (this.data.scrollTimer) {
      clearInterval(this.data.scrollTimer);
      this.data.scrollTimer = null;
    }
    
    // 清除恢复滚动的定时器
    if (this.data.resumeScrollTimer) {
      clearTimeout(this.data.resumeScrollTimer);
      this.data.resumeScrollTimer = null;
    }
  },
  
  // 从数据库获取最新通知
  fetchLatestNotices() {
    // 检查页面是否可见
    if (!this.data || !this.data.isPageVisible) {
      return;
    }
    
    //console.log('开始获取最新通知数据');
    
    // 获取数据库引用
    const db = wx.cloud.database();
    
    // 查询notice集合，按createTime降序排列，限制20条
    db.collection('notice')
      .orderBy('createTime', 'desc') // 按创建时间降序
      .where({
        isPublic: true, // 只获取公开的通知
        isDeleted: false // 不包括已删除的通知
      })
      .limit(20) // 最多获取20条
      .get()
      .then(res => {
        //console.log('成功获取通知数据:', res.data.length);
        
        if (res.data && res.data.length > 0) {
          // 处理通知数据，添加时间信息
          const noticeListWithTime = res.data.map((item, index) => {
            // 获取时间文本
            let timeText = this.getTimeText(item.createTime);
            
            // 提取标题（植物名称及规格）
            let titleText = '';
            
            // 如果存在植物名称，优先使用它作为标题
            if (item.plantName) {
              // 如果内容中包含植物名称，尝试提取更完整的标题
              if (item.noticeText && item.noticeText.includes(item.plantName)) {
                // 匹配植物名称及其后的规格信息
                const fullPattern = new RegExp(`${item.plantName}[，,][^，,]*[，,]?`);
                const matchFull = item.noticeText.match(fullPattern);
                
                if (matchFull && matchFull[0]) {
                  titleText = matchFull[0];
                } else {
                  titleText = item.plantName;
                }
              } else {
                titleText = item.plantName;
              }
            } else if (item.noticeText) {
              // 植物名称不存在，尝试从文本中提取
              const titleMatch = item.noticeText.match(/^([^，,]+[，,][^，,]+[，,]?)/);
              if (titleMatch && titleMatch[1]) {
                titleText = titleMatch[1];
              }
            }
            
            // 确定帖子类型
            let postType = item.postType || 'unknown';
            
            // 获取发布者昵称 - 直接使用publisherName字段，并进行隐私处理
            let originalNickname = item.publisherName || 'riki';
            // 只显示第一个字符，后面的字符用"*"替换
            let publisherNickname = originalNickname.length > 1 
              ? originalNickname.charAt(0) + '*'.repeat(originalNickname.length - 1)
              : originalNickname;
            
            // 添加本地ID用于列表渲染
            const notice = {
              id: index + 1,
              originalId: item._id,
              postId: item.postId,
              postType: postType,
              publisherId: item.publisherId,
              publisherNickname: publisherNickname,
              timeText: timeText,
              titleText: titleText
            };
            return notice;
          });
          
          // 更新通知列表数据
          this.setData({
            noticeList: noticeListWithTime
          });
          
          // 重新初始化滚动
          setTimeout(() => {
            if (this.data.isPageVisible) {
              this.initNoticeScroll();
            }
          }, 300);
        } else {
       
          // 如果没有数据，清空通知列表
          this.setData({
            noticeList: []
          });
        }
      })
      .catch(err => {
     
        // 获取失败时不改变现有数据
      });
  },
  
  /**
   * 获取时间文本
   * @param {Object|Date} createTime 创建时间
   * @return {String} 时间文本
   */
  getTimeText(createTime) {
    if (!createTime) {
      return '';
    }
    
    // 将服务器时间转换为JS Date对象
    let timeObj;
    if (typeof createTime === 'object' && createTime.toDate) {
      // 处理服务器时间对象
      timeObj = createTime.toDate();
    } else if (createTime instanceof Date) {
      // 已经是Date对象
      timeObj = createTime;
    } else {
      // 尝试解析其他格式
      timeObj = new Date(createTime);
    }
    
    // 计算时间差
    const now = new Date();
    const diffMs = now.getTime() - timeObj.getTime();
    const diffSecs = Math.floor(diffMs / 1000);
    const diffMins = Math.floor(diffSecs / 60);
    const diffHours = Math.floor(diffMins / 60);
    const diffDays = Math.floor(diffHours / 24);
    
    // 根据时间差显示不同格式
    let timeText = '';
    if (diffSecs < 60) {
      timeText = '刚刚';
    } else if (diffMins < 60) {
      timeText = `${diffMins}分钟前`;
    } else if (diffHours < 24) {
      timeText = `${diffHours}小时前`;
    } else if (diffDays < 30) {
      timeText = `${diffDays}天前`;
    } else {
      // 超过30天显示具体日期
      const year = timeObj.getFullYear();
      const month = timeObj.getMonth() + 1;
      const day = timeObj.getDate();
      timeText = `${year}-${month}-${day}`;
    }
    
    return timeText;
  },

  // 获取平台统计数据
  fetchPlatformStats() {
    // 如果页面不可见，不获取数据
    if (!this.data.isPageVisible) {
      return;
    }
    
    // 获取数据库引用
    const db = wx.cloud.database();
    
    // 从指定ID的记录中读取统计数据
    db.collection('appData').doc('61493796683c0eae01a1f4045d9b0e0b').get()
      .then(res => {
        if (res.data) {
          // 获取到数据后格式化并更新状态
          const statsData = {
            postsCount: this.formatNumber(res.data.totalPost || 0),
            loginsCount: this.formatNumber(res.data.totalLogined || 0),
            usersCount: this.formatNumber(res.data.totalUsers || 0),
            activePostsCount: this.formatNumber(res.data.totalView || 0)
          };
          
          // 更新状态
          this.setData({ statsData });
        } else {
       
          // 使用默认值
          this.setData({
            statsData: {
              postsCount: '2,568',
              loginsCount: '15,342',
              usersCount: '872',
              activePostsCount: '1,245'
            }
          });
        }
      })
      .catch(err => {
      
        // 出错时使用默认值满
        this.setData({
          statsData: {
            postsCount: '2,568',
            loginsCount: '15,342',
            usersCount: '872',
            activePostsCount: '1,245'
          }
        });
      });
  },
  
  // 格式化数字为缩略显示（>=10000时使用万位缩略）
  formatNumber(num) {
    const number = parseInt(num);

    if (number >= 10000) {
      const wan = number / 10000;

      if (wan >= 100) {
        // 100万及以上：显示为 xxx.xw
        return Math.floor(wan * 10) / 10 + 'w';
      } else if (wan >= 10) {
        // 10万-99万：显示为 xx.xw
        return Math.floor(wan * 10) / 10 + 'w';
      } else {
        // 1万-9万：显示为 x.xw
        return Math.floor(wan * 10) / 10 + 'w';
      }
    } else {
      // 小于10000：使用千位分隔符
      return number.toString().replace(/\B(?=(\d{3})+(?!\d))/g, ",");
    }
  },

  /**
   * 退林还耕卡片点击事件
   */
  onForestReturnTap() {
    // 如果欢迎页面正在显示，先关闭欢迎页面
    if (this.data.showWelcomeScreen) {
      this.onWelcomeClose();
      return;
    }
    
    if (this.checkLoginStatus()) {
      this._cleanupBeforeNavigation();
      wx.navigateTo({
        url: '/pages/returnPublic/returnPublic'
      });
    } else {
      // 显示登录提示
      wx.showModal({
        title: '提示',
        content: '需要先登录才能使用退林还耕服务',
        confirmText: '去登录',
        success: (res) => {
          if (res.confirm) {
            // 跳转到用户中心页面
            wx.switchTab({
              url: '/pages/user/user'
            });
          }
        }
      });
    }
  },
  
  /**
   * mpzr点击
   */
  onNurseryTap() {
    
    if (this.data.showWelcomeScreen) {
      this.onWelcomeClose();
      return;
    }

    
    const db = wx.cloud.database();
    db.collection('appData').doc('61493796683c0eae01a1f4045d9b0e0b').get()
      .then(res => {
        if (res.data && res.data.Istime) {
          const currentTime = new Date().getTime();
          const targetTime = new Date(res.data.Istime).getTime();

          if (currentTime < targetTime) {
            wx.showToast({
              title: '功能已关闭',
              icon: 'none',
              duration: 2000
            });
            return;
          }
        }

        
        this._cleanupBeforeNavigation();
        wx.navigateTo({
          url: '/pages/nursery/nursery'
        });
      })
      .catch(err => {
      
        this._cleanupBeforeNavigation();
        wx.navigateTo({
          url: '/pages/nursery/nursery'
        });
      });
  },
  
  /**
   * 工程报价卡片点击事件
   */
  onDesignTap() {
    // 清理资源
    this._cleanupBeforeNavigation();

    // 导航到工程报价页面
    wx.navigateTo({
      url: '/pages/quote/quote',
      fail: (err) => {
        console.error('导航到工程报价页面失败:', err);
        wx.showToast({
          title: '页面跳转失败',
          icon: 'none'
        });
      }
    });
  },

  /**
   * 视频教程卡片点击事件
   */
  onVideoTutorialTap() {
    // 如果欢迎页面正在显示，先关闭欢迎页面
    if (this.data.showWelcomeScreen) {
      this.onWelcomeClose();
      return;
    }

    // 跳转到视频教程页面
    wx.navigateTo({
      url: '/pages/tutorial/tutorial'
    });
  },
  
  /**
   * 客服电话拨打
   */
  callCustomerService() {
    wx.showModal({
      title: '联系客服',
      content: '是否拨打客服电话：18384104768？',
      confirmText: '拨打',
      cancelText: '取消',
      success: (res) => {
        if (res.confirm) {
          wx.makePhoneCall({
            phoneNumber: '18384104768',
            fail: (err) => {
              console.error('拨打电话失败:', err);
              wx.showToast({
                title: '拨打失败，请手动拨号',
                icon: 'none'
              });
            }
          });
        }
      }
    });
  }
});


