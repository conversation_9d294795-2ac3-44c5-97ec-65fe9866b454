// 云函数 - checkBanStatus
// 检查用户是否在黑名单中
const cloud = require('wx-server-sdk')

cloud.init({
  env: cloud.DYNAMIC_CURRENT_ENV
})

const db = cloud.database()

/**
 * 检查用户是否在黑名单中
 * @param {Object} event - 云函数事件参数
 * @param {string} event.openid - 可选，指定要检查的openid，如果不提供则使用当前用户的openid
 * @param {Object} context - 云函数上下文
 * @returns {Object} 检查结果
 */
exports.main = async (event, context) => {
  try {
    // 获取用户openid
    const wxContext = cloud.getWXContext()
    const currentOpenid = wxContext.OPENID
    
    // 使用传入的openid或当前用户的openid
    const targetOpenid = event.openid || currentOpenid
    
    if (!targetOpenid) {
      return {
        success: false,
        isBanned: false,
        message: '无法获取用户标识',
        code: 'NO_OPENID'
      }
    }
    
    console.log('检查用户封禁状态，openid:', targetOpenid)
    
    // 查询BanList集合，检查用户是否在黑名单中
    const banResult = await db.collection('BanList')
      .where({
        openid: targetOpenid,
        status: 'active'  // 只查询状态为active的封禁记录
      })
      .limit(1)
      .get()
    
    const isBanned = banResult.data.length > 0
    
    if (isBanned) {
      const banRecord = banResult.data[0]
      console.log('用户已被封禁:', {
        openid: targetOpenid,
        banReason: banRecord.reason,
        banTime: banRecord.banTime
      })
      
      return {
        success: true,
        isBanned: true,
        message: '用户已被封禁',
        code: 'USER_BANNED',
        banInfo: {
          reason: banRecord.reason || '违反平台规定',
          banTime: banRecord.banTime,
          banId: banRecord._id
        }
      }
    } else {
      console.log('用户未被封禁:', targetOpenid)
      
      return {
        success: true,
        isBanned: false,
        message: '用户状态正常',
        code: 'USER_NORMAL'
      }
    }
    
  } catch (error) {
    console.error('检查用户封禁状态失败:', error)
    
    return {
      success: false,
      isBanned: false,
      message: '检查用户状态时发生错误',
      code: 'CHECK_ERROR',
      error: error.message
    }
  }
}
