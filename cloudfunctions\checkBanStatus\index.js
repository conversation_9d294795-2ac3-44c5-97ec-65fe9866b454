// 云函数：checkBanStatus
// 用于检查用户是否在黑名单中
const cloud = require('wx-server-sdk')

cloud.init({
  env: cloud.DYNAMIC_CURRENT_ENV
})

const db = cloud.database()

/**
 * 检查用户是否在黑名单中
 * @param {Object} event - 云函数事件参数
 * @param {string} event.openid - 用户的openid（可选，如果不提供则使用当前用户）
 * @param {Object} context - 云函数上下文
 * @returns {Object} 检查结果
 */
exports.main = async (event, context) => {
  try {
    // 获取微信上下文
    const wxContext = cloud.getWXContext()
    const currentOpenid = wxContext.OPENID
    
    // 使用传入的openid或当前用户的openid
    const targetOpenid = event.openid || currentOpenid
    
    if (!targetOpenid) {
      return {
        success: false,
        isBanned: false,
        message: '无法获取用户身份信息',
        code: 'NO_OPENID'
      }
    }
    
    console.log('检查用户黑名单状态，openid:', targetOpenid)
    
    // 查询黑名单集合
    const banResult = await db.collection('BanList')
      .where({
        openid: targetOpenid,
        status: 'active' // 只查询激活状态的封禁记录
      })
      .orderBy('createTime', 'desc')
      .limit(1)
      .get()
    
    if (banResult.data.length > 0) {
      const banRecord = banResult.data[0]
      
      // 检查是否有过期时间，如果有且已过期，则不算被封禁
      if (banRecord.expireTime) {
        const now = new Date()
        const expireTime = new Date(banRecord.expireTime)
        
        if (now > expireTime) {
          // 封禁已过期，自动更新状态为过期
          await db.collection('BanList').doc(banRecord._id).update({
            data: {
              status: 'expired',
              updateTime: db.serverDate()
            }
          })
          
          console.log('用户封禁已过期，自动解除封禁:', targetOpenid)
          return {
            success: true,
            isBanned: false,
            message: '用户状态正常',
            code: 'NOT_BANNED'
          }
        }
      }
      
      // 用户在黑名单中且未过期
      console.log('用户在黑名单中:', targetOpenid, banRecord)
      return {
        success: true,
        isBanned: true,
        message: banRecord.reason || '您的账号已被封禁，如有疑问请联系客服',
        banReason: banRecord.reason,
        banTime: banRecord.createTime,
        expireTime: banRecord.expireTime,
        code: 'BANNED'
      }
    }
    
    // 用户不在黑名单中
    console.log('用户状态正常:', targetOpenid)
    return {
      success: true,
      isBanned: false,
      message: '用户状态正常',
      code: 'NOT_BANNED'
    }
    
  } catch (error) {
    console.error('检查黑名单状态失败:', error)
    return {
      success: false,
      isBanned: false,
      message: '检查用户状态失败，请重试',
      error: error.message,
      code: 'CHECK_ERROR'
    }
  }
}
