// banUtils.js
// 黑名单验证工具模块

/**
 * 检查用户是否在黑名单中
 * @param {string} openid 用户的openid（可选）
 * @returns {Promise<Object>} 检查结果
 */
const checkUserBanStatus = (openid = null) => {
  return new Promise((resolve, reject) => {
    wx.cloud.callFunction({
      name: 'checkBanStatus',
      data: {
        openid: openid
      },
      success: (res) => {
        console.log('黑名单检查结果:', res.result)
        resolve(res.result)
      },
      fail: (err) => {
        console.error('黑名单检查失败:', err)
        // 检查失败时默认允许登录，避免因网络问题影响正常用户
        resolve({
          success: false,
          isBanned: false,
          message: '网络异常，请稍后重试',
          code: 'NETWORK_ERROR'
        })
      }
    })
  })
}

/**
 * 处理用户被封禁的情况
 * @param {Object} banInfo 封禁信息
 * @param {Function} onBanned 被封禁时的回调函数
 */
const handleUserBanned = (banInfo, onBanned = null) => {
  console.log('用户被封禁:', banInfo)
  
  // 清除本地登录状态
  const app = getApp()
  if (app && app.clearLoginState) {
    app.clearLoginState()
  }
  
  // 显示封禁提示
  let message = banInfo.message || '您的账号已被封禁'
  
  // 如果有过期时间，显示解封时间
  if (banInfo.expireTime) {
    const expireDate = new Date(banInfo.expireTime)
    const expireDateStr = expireDate.toLocaleString('zh-CN', {
      year: 'numeric',
      month: '2-digit',
      day: '2-digit',
      hour: '2-digit',
      minute: '2-digit'
    })
    message += `\n解封时间：${expireDateStr}`
  }
  
  wx.showModal({
    title: '账号已被封禁',
    content: message,
    showCancel: false,
    confirmText: '我知道了',
    success: () => {
      if (onBanned && typeof onBanned === 'function') {
        onBanned(banInfo)
      }
    }
  })
}

/**
 * 在登录前检查用户状态
 * @param {string} openid 用户的openid
 * @returns {Promise<boolean>} 是否允许登录
 */
const checkBeforeLogin = async (openid = null) => {
  try {
    const result = await checkUserBanStatus(openid)
    
    if (result.isBanned) {
      handleUserBanned(result)
      return false
    }
    
    return true
  } catch (error) {
    console.error('登录前检查失败:', error)
    // 检查失败时允许登录，避免影响正常用户
    return true
  }
}

/**
 * 在应用启动时检查已登录用户的状态
 * @returns {Promise<boolean>} 用户状态是否正常
 */
const checkLoggedInUserStatus = async () => {
  try {
    const app = getApp()
    
    // 只有已登录用户才需要检查
    if (!app.globalData.isLogined || !app.globalData.openid) {
      return true
    }
    
    const result = await checkUserBanStatus(app.globalData.openid)
    
    if (result.isBanned) {
      handleUserBanned(result, () => {
        // 被封禁后跳转到首页
        wx.reLaunch({
          url: '/pages/home/<USER>'
        })
      })
      return false
    }
    
    return true
  } catch (error) {
    console.error('检查已登录用户状态失败:', error)
    return true
  }
}

/**
 * 创建黑名单记录（管理员功能）
 * @param {string} openid 要封禁的用户openid
 * @param {string} reason 封禁原因
 * @param {Date} expireTime 过期时间（可选，永久封禁则不传）
 * @returns {Promise<Object>} 操作结果
 */
const addToBanList = (openid, reason, expireTime = null) => {
  return new Promise((resolve, reject) => {
    const db = wx.cloud.database()
    
    const banData = {
      openid: openid,
      reason: reason,
      status: 'active',
      createTime: db.serverDate(),
      updateTime: db.serverDate()
    }
    
    if (expireTime) {
      banData.expireTime = expireTime
    }
    
    db.collection('BanList').add({
      data: banData,
      success: (res) => {
        console.log('添加黑名单成功:', res)
        resolve({
          success: true,
          message: '用户已被封禁',
          banId: res._id
        })
      },
      fail: (err) => {
        console.error('添加黑名单失败:', err)
        reject(err)
      }
    })
  })
}

/**
 * 解除用户封禁（管理员功能）
 * @param {string} openid 要解封的用户openid
 * @returns {Promise<Object>} 操作结果
 */
const removeFromBanList = (openid) => {
  return new Promise((resolve, reject) => {
    const db = wx.cloud.database()
    
    db.collection('BanList')
      .where({
        openid: openid,
        status: 'active'
      })
      .update({
        data: {
          status: 'removed',
          updateTime: db.serverDate()
        },
        success: (res) => {
          console.log('解除封禁成功:', res)
          resolve({
            success: true,
            message: '用户已解封'
          })
        },
        fail: (err) => {
          console.error('解除封禁失败:', err)
          reject(err)
        }
      })
  })
}

module.exports = {
  checkUserBanStatus,
  handleUserBanned,
  checkBeforeLogin,
  checkLoggedInUserStatus,
  addToBanList,
  removeFromBanList
}
