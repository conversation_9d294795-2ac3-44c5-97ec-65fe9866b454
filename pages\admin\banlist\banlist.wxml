<!--pages/admin/banlist/banlist.wxml-->
<view class="container" wx:if="{{isAdmin}}">
  <!-- 页面标题 -->
  <view class="header">
    <text class="title">黑名单管理</text>
    <view class="stats">
      <text class="count">共 {{total}} 条记录</text>
    </view>
  </view>

  <!-- 操作按钮 -->
  <view class="actions">
    <button class="add-btn" bindtap="showAddBanModal">添加封禁</button>
  </view>

  <!-- 黑名单列表 -->
  <view class="ban-list">
    <view class="ban-item" wx:for="{{banList}}" wx:key="_id">
      <view class="ban-info">
        <view class="openid">
          <text class="label">OpenID:</text>
          <text class="value">{{item.openid}}</text>
        </view>
        <view class="reason">
          <text class="label">封禁原因:</text>
          <text class="value">{{item.reason}}</text>
        </view>
        <view class="time-info">
          <view class="create-time">
            <text class="label">封禁时间:</text>
            <text class="value">{{formatTime(item.createTime)}}</text>
          </view>
          <view class="expire-time">
            <text class="label">过期时间:</text>
            <text class="value">{{formatTime(item.expireTime)}}</text>
          </view>
        </view>
        <view class="status">
          <text class="label">状态:</text>
          <text class="value status-{{item.status}}">
            {{item.status === 'active' ? '生效中' : item.status === 'expired' ? '已过期' : '已解除'}}
          </text>
        </view>
      </view>
      
      <view class="ban-actions" wx:if="{{item.status === 'active'}}">
        <button class="remove-btn" data-openid="{{item.openid}}" bindtap="removeBan">解除封禁</button>
      </view>
    </view>
  </view>

  <!-- 加载状态 -->
  <view class="loading" wx:if="{{loading}}">
    <text>加载中...</text>
  </view>

  <!-- 空状态 -->
  <view class="empty" wx:if="{{!loading && banList.length === 0}}">
    <text>暂无封禁记录</text>
  </view>
</view>

<!-- 添加封禁模态框 -->
<view class="modal-overlay" wx:if="{{showAddBanModal}}" bindtap="hideAddBanModal">
  <view class="modal-content" catchtap="">
    <view class="modal-header">
      <text class="modal-title">添加封禁</text>
      <text class="modal-close" bindtap="hideAddBanModal">×</text>
    </view>
    
    <view class="modal-body">
      <view class="form-item">
        <text class="form-label">用户OpenID</text>
        <input class="form-input" 
               placeholder="请输入要封禁的用户OpenID" 
               value="{{newBan.openid}}"
               data-field="openid"
               bindinput="onInputChange" />
      </view>
      
      <view class="form-item">
        <text class="form-label">封禁原因</text>
        <textarea class="form-textarea" 
                  placeholder="请输入封禁原因" 
                  value="{{newBan.reason}}"
                  data-field="reason"
                  bindinput="onInputChange"></textarea>
      </view>
      
      <view class="form-item">
        <view class="checkbox-item">
          <checkbox checked="{{newBan.isPermanent}}" bindtap="togglePermanent" />
          <text class="checkbox-label">永久封禁</text>
        </view>
      </view>
      
      <view class="form-item" wx:if="{{!newBan.isPermanent}}">
        <text class="form-label">过期时间</text>
        <picker mode="multiSelector" 
                range="{{dateTimeRange}}" 
                value="{{dateTimeValue}}"
                bindchange="onExpireTimeChange">
          <view class="picker-display">
            {{newBan.expireTime || '请选择过期时间'}}
          </view>
        </picker>
      </view>
    </view>
    
    <view class="modal-footer">
      <button class="cancel-btn" bindtap="hideAddBanModal">取消</button>
      <button class="confirm-btn" bindtap="addBan">确认封禁</button>
    </view>
  </view>
</view>
