// debugBanUtils.js
// 黑名单功能调试工具

/**
 * 调试黑名单功能 - 检查所有可能的问题
 */
const debugBanFunction = async () => {
  console.log('=== 开始调试黑名单功能 ===');
  
  try {
    // 1. 检查当前用户的 openid
    const app = getApp();
    const currentOpenid = app.globalData.openid;
    console.log('1. 当前用户 openid:', currentOpenid);
    
    if (!currentOpenid) {
      console.log('❌ 当前用户未登录或 openid 为空');
      return;
    }
    
    // 2. 检查云函数是否可以调用
    console.log('2. 测试云函数调用...');
    try {
      const testResult = await wx.cloud.callFunction({
        name: 'checkBanStatus',
        data: {
          openid: currentOpenid
        }
      });
      console.log('✅ 云函数调用成功:', testResult.result);
    } catch (err) {
      console.log('❌ 云函数调用失败:', err);
      console.log('请检查云函数是否已正确部署');
      return;
    }
    
    // 3. 直接查询数据库中的黑名单记录
    console.log('3. 查询数据库中的黑名单记录...');
    const db = wx.cloud.database();
    
    try {
      const banResult = await db.collection('BanList')
        .where({
          openid: currentOpenid
        })
        .get();
      
      console.log('数据库查询结果:', banResult);
      
      if (banResult.data.length === 0) {
        console.log('❌ 数据库中没有找到该用户的黑名单记录');
        console.log('请检查：');
        console.log('- BanList 集合是否存在');
        console.log('- openid 是否正确添加');
        console.log('- 数据库权限是否正确设置');
      } else {
        console.log('✅ 找到黑名单记录:', banResult.data);
        
        // 检查记录状态
        const activeRecords = banResult.data.filter(record => record.status === 'active');
        if (activeRecords.length === 0) {
          console.log('❌ 没有状态为 "active" 的记录');
          console.log('请确保 status 字段设置为 "active"');
        } else {
          console.log('✅ 找到激活状态的黑名单记录:', activeRecords);
        }
      }
    } catch (err) {
      console.log('❌ 数据库查询失败:', err);
      console.log('请检查 BanList 集合是否存在，权限是否正确');
    }
    
    // 4. 检查登录流程是否调用了黑名单检查
    console.log('4. 检查登录流程...');
    console.log('请尝试退出登录后重新登录，观察控制台是否有黑名单检查的日志');
    
    // 5. 手动测试黑名单检查
    console.log('5. 手动测试黑名单检查...');
    const banUtils = require('./banUtils.js');
    
    try {
      const checkResult = await banUtils.checkUserBanStatus(currentOpenid);
      console.log('手动检查结果:', checkResult);
      
      if (checkResult.isBanned) {
        console.log('✅ 黑名单检查正常，用户应该被封禁');
        console.log('如果用户仍能登录，请检查登录流程是否正确调用了黑名单检查');
      } else {
        console.log('❌ 黑名单检查返回用户未被封禁');
        console.log('请检查数据库记录和云函数逻辑');
      }
    } catch (err) {
      console.log('❌ 手动检查失败:', err);
    }
    
  } catch (error) {
    console.log('❌ 调试过程中发生错误:', error);
  }
  
  console.log('=== 调试完成 ===');
};

/**
 * 检查数据库中的黑名单记录格式
 */
const checkBanListFormat = async () => {
  console.log('=== 检查黑名单记录格式 ===');
  
  const db = wx.cloud.database();
  
  try {
    const result = await db.collection('BanList').limit(10).get();
    
    if (result.data.length === 0) {
      console.log('❌ BanList 集合为空');
      return;
    }
    
    console.log('BanList 集合中的记录:');
    result.data.forEach((record, index) => {
      console.log(`记录 ${index + 1}:`, {
        _id: record._id,
        openid: record.openid,
        reason: record.reason,
        status: record.status,
        createTime: record.createTime,
        expireTime: record.expireTime
      });
      
      // 检查必要字段
      if (!record.openid) {
        console.log(`❌ 记录 ${index + 1} 缺少 openid 字段`);
      }
      if (!record.status) {
        console.log(`❌ 记录 ${index + 1} 缺少 status 字段`);
      }
      if (record.status !== 'active' && record.status !== 'expired' && record.status !== 'removed') {
        console.log(`❌ 记录 ${index + 1} status 字段值不正确: ${record.status}`);
      }
    });
    
  } catch (err) {
    console.log('❌ 查询 BanList 集合失败:', err);
  }
};

/**
 * 测试添加一个标准格式的黑名单记录
 */
const addTestBanRecord = async (testOpenid) => {
  console.log('=== 添加测试黑名单记录 ===');
  
  const db = wx.cloud.database();
  
  const banData = {
    openid: testOpenid,
    reason: '调试测试封禁',
    status: 'active',
    createTime: new Date(),
    updateTime: new Date()
  };
  
  try {
    const result = await db.collection('BanList').add({
      data: banData
    });
    
    console.log('✅ 测试记录添加成功:', result);
    console.log('记录数据:', banData);
    
    return result._id;
  } catch (err) {
    console.log('❌ 添加测试记录失败:', err);
    return null;
  }
};

/**
 * 删除测试记录
 */
const removeTestBanRecord = async (recordId) => {
  console.log('=== 删除测试记录 ===');
  
  const db = wx.cloud.database();
  
  try {
    const result = await db.collection('BanList').doc(recordId).remove();
    console.log('✅ 测试记录删除成功:', result);
  } catch (err) {
    console.log('❌ 删除测试记录失败:', err);
  }
};

/**
 * 完整的调试流程
 */
const runFullDebug = async () => {
  console.log('🔍 开始完整调试流程...');
  
  // 1. 基础调试
  await debugBanFunction();
  
  // 2. 检查记录格式
  await checkBanListFormat();
  
  // 3. 如果当前用户有 openid，测试添加和检查
  const app = getApp();
  const currentOpenid = app.globalData.openid;
  
  if (currentOpenid) {
    console.log('🧪 开始测试流程...');
    
    // 添加测试记录
    const testRecordId = await addTestBanRecord(currentOpenid);
    
    if (testRecordId) {
      // 等待一下，然后测试检查
      setTimeout(async () => {
        await debugBanFunction();
        
        // 清理测试记录
        await removeTestBanRecord(testRecordId);
      }, 2000);
    }
  }
  
  console.log('🔍 调试流程完成');
};

module.exports = {
  debugBanFunction,
  checkBanListFormat,
  addTestBanRecord,
  removeTestBanRecord,
  runFullDebug
};
