// 在小程序控制台中运行这段代码来调试黑名单功能

// 1. 检查当前用户 openid
console.log('当前用户 openid:', getApp().globalData.openid);

// 2. 查询数据库中的所有黑名单记录
const db = wx.cloud.database();
db.collection('BanList').get().then(res => {
  console.log('BanList 集合中的所有记录:', res.data);
  
  // 检查是否有匹配的记录
  const targetOpenid = 'o5n0D7ssxYCHCBpaDCn3qix8NOKQ';
  const matchingRecords = res.data.filter(record => record.openid === targetOpenid);
  
  console.log('匹配的记录:', matchingRecords);
  
  if (matchingRecords.length === 0) {
    console.log('❌ 没有找到匹配的记录，请检查：');
    console.log('1. openid 是否正确');
    console.log('2. 记录是否真的添加到了数据库');
  } else {
    matchingRecords.forEach((record, index) => {
      console.log(`记录 ${index + 1}:`, record);
      
      // 检查关键字段
      if (record.status !== 'active') {
        console.log(`❌ 记录 ${index + 1} 的 status 不是 'active':`, record.status);
      } else {
        console.log(`✅ 记录 ${index + 1} 的 status 正确`);
      }
    });
  }
}).catch(err => {
  console.log('❌ 查询失败:', err);
});

// 3. 手动添加一个正确格式的测试记录
function addCorrectBanRecord() {
  const db = wx.cloud.database();
  
  const correctRecord = {
    openid: 'o5n0D7ssxYCHCBpaDCn3qix8NOKQ',
    reason: '测试封禁 - 正确格式',
    status: 'active',
    createTime: new Date(),
    updateTime: new Date()
  };
  
  db.collection('BanList').add({
    data: correctRecord
  }).then(res => {
    console.log('✅ 正确格式的测试记录添加成功:', res);
    console.log('记录数据:', correctRecord);
    
    // 添加成功后立即测试
    setTimeout(() => {
      wx.cloud.callFunction({
        name: 'checkBanStatus',
        data: {
          openid: 'o5n0D7ssxYCHCBpaDCn3qix8NOKQ'
        },
        success: (result) => {
          console.log('测试结果:', result.result);
          if (result.result.isBanned) {
            console.log('✅ 黑名单功能正常工作！');
          } else {
            console.log('❌ 黑名单功能仍然不工作');
          }
        }
      });
    }, 1000);
    
  }).catch(err => {
    console.log('❌ 添加测试记录失败:', err);
  });
}

// 4. 清理测试记录
function cleanTestRecords() {
  const db = wx.cloud.database();
  
  db.collection('BanList').where({
    openid: 'o5n0D7ssxYCHCBpaDCn3qix8NOKQ',
    reason: db.RegExp({
      regexp: '测试',
      options: 'i'
    })
  }).remove().then(res => {
    console.log('✅ 测试记录清理完成:', res);
  }).catch(err => {
    console.log('❌ 清理测试记录失败:', err);
  });
}

console.log('调试工具已加载，可以使用以下命令：');
console.log('addCorrectBanRecord() - 添加正确格式的测试记录');
console.log('cleanTestRecords() - 清理测试记录');
